# Gaming Store - PlayStation Booking System

A modern Next.js web application for a gaming store booking system where customers can reserve time slots to play PlayStation, with an admin dashboard for store management and analytics.

## 🎮 Features

### Customer Features
- **Browse Gaming Stations**: View available PlayStation and gaming setups
- **Real-time Booking**: Reserve time slots with live availability checking
- **User Authentication**: Secure customer account system
- **Booking Management**: View, manage, and cancel reservations
- **Responsive Design**: Works seamlessly on desktop, tablet, and mobile

### Admin Features
- **Admin Dashboard**: Comprehensive management interface
- **Reservation Management**: View and manage all customer bookings
- **Analytics & Reporting**: Revenue tracking, booking statistics, occupancy rates
- **Station Management**: Add, edit, and configure gaming stations
- **Customer Management**: View customer information and booking history

## 🛠️ Tech Stack

- **Framework**: Next.js 15 (App Router)
- **Database**: MongoDB with Mongoose
- **Authentication**: NextAuth.js
- **Styling**: Tailwind CSS
- **Language**: TypeScript
- **UI Components**: Headless UI, Heroicons

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- MongoDB (local or cloud instance)
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd gaming-store
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   Create a `.env.local` file in the root directory:
   ```env
   # MongoDB Connection
   MONGODB_URI=mongodb://localhost:27017/gaming-store

   # NextAuth Configuration
   NEXTAUTH_URL=http://localhost:3000
   NEXTAUTH_SECRET=your-secret-key-here

   # JWT Secret
   JWT_SECRET=your-jwt-secret-here

   # Admin Credentials (for initial setup)
   ADMIN_EMAIL=<EMAIL>
   ADMIN_PASSWORD=admin123
   ```

4. **Set up the database**
   ```bash
   # Run the seed script to populate initial data
   npx ts-node src/scripts/seed.ts
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📱 Usage

### Default Accounts
After running the seed script, you can use these accounts:

**Admin Account:**
- Email: `<EMAIL>`
- Password: `admin123`

**Customer Account:**
- Email: `<EMAIL>`
- Password: `customer123`

### Customer Workflow
1. Sign up or sign in to your account
2. Browse available gaming stations
3. Select a station and choose your preferred date/time
4. Confirm your booking
5. Manage your bookings from the "My Bookings" page

### Admin Workflow
1. Sign in with admin credentials
2. Access the admin dashboard
3. View analytics and revenue reports
4. Manage customer bookings
5. Configure gaming stations and pricing

## 🏗️ Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── admin/             # Admin dashboard
│   ├── api/               # API routes
│   ├── auth/              # Authentication pages
│   ├── bookings/          # Customer booking pages
│   └── stations/          # Gaming station pages
├── lib/                   # Utility libraries
│   ├── auth.ts           # NextAuth configuration
│   ├── auth-utils.ts     # Authentication utilities
│   └── mongodb.ts        # Database connection
├── models/               # MongoDB schemas
│   ├── User.ts
│   ├── GamingStation.ts
│   └── Booking.ts
├── scripts/              # Database scripts
│   └── seed.ts          # Database seeding
└── types/               # TypeScript definitions
```

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/[...nextauth]` - NextAuth endpoints

### Gaming Stations
- `GET /api/stations` - Get all active stations
- `POST /api/stations` - Create new station (admin only)
- `GET /api/stations/[id]/availability` - Check availability

### Bookings
- `GET /api/bookings` - Get user bookings (or all for admin)
- `POST /api/bookings` - Create new booking
- `PATCH /api/bookings/[id]` - Update booking status
- `DELETE /api/bookings/[id]` - Cancel booking

### Admin
- `GET /api/admin/analytics` - Get dashboard analytics

## 🎨 Design Features

- **Modern UI**: Glass morphism effects and gradient backgrounds
- **Responsive Layout**: Mobile-first design approach
- **Dark Theme**: Gaming-focused dark color scheme
- **Smooth Animations**: Hover effects and transitions
- **Accessibility**: Proper ARIA labels and keyboard navigation

## 📊 Analytics Features

The admin dashboard provides comprehensive analytics:
- **Revenue Tracking**: Daily, weekly, and monthly revenue
- **Booking Statistics**: Total bookings and trends
- **Occupancy Rates**: Station utilization metrics
- **Customer Insights**: Booking patterns and preferences

## 🔒 Security Features

- **Authentication**: Secure user authentication with NextAuth.js
- **Authorization**: Role-based access control (customer/admin)
- **Password Hashing**: Bcrypt for secure password storage
- **Input Validation**: Server-side validation for all inputs
- **CSRF Protection**: Built-in protection against CSRF attacks

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically

### Other Platforms
The application can be deployed on any platform that supports Node.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the documentation
- Review the code comments for implementation details
