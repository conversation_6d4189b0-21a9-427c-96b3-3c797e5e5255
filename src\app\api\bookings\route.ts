import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Booking from '@/models/Booking';
import GamingStation from '@/models/GamingStation';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    let bookings;
    
    if (session.user.role === 'admin') {
      // Admin can see all bookings
      bookings = await Booking.find()
        .populate('user', 'name email')
        .populate('gamingStation', 'name type')
        .sort({ createdAt: -1 });
    } else {
      // Customers can only see their own bookings
      bookings = await Booking.find({ user: session.user.id })
        .populate('gamingStation', 'name type')
        .sort({ createdAt: -1 });
    }

    return NextResponse.json({ bookings });
  } catch (error) {
    console.error('Error fetching bookings:', error);
    return NextResponse.json(
      { error: 'Failed to fetch bookings' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { gamingStationId, startTime, endTime, duration } = await request.json();

    if (!gamingStationId || !startTime || !endTime || !duration) {
      return NextResponse.json(
        { error: 'Gaming station, start time, end time, and duration are required' },
        { status: 400 }
      );
    }

    await connectDB();

    // Check if gaming station exists and is active
    const station = await GamingStation.findById(gamingStationId);
    if (!station || !station.isActive) {
      return NextResponse.json(
        { error: 'Gaming station not found or inactive' },
        { status: 404 }
      );
    }

    // Check for conflicting bookings
    const conflictingBooking = await Booking.findOne({
      gamingStation: gamingStationId,
      status: { $in: ['confirmed', 'in-progress'] },
      $or: [
        {
          startTime: { $lt: new Date(endTime) },
          endTime: { $gt: new Date(startTime) }
        }
      ]
    });

    if (conflictingBooking) {
      return NextResponse.json(
        { error: 'Time slot is already booked' },
        { status: 409 }
      );
    }

    // Calculate total amount
    const totalAmount = duration * station.hourlyRate;

    // Create booking
    const booking = new Booking({
      user: session.user.id,
      gamingStation: gamingStationId,
      startTime: new Date(startTime),
      endTime: new Date(endTime),
      duration,
      totalAmount,
      status: 'confirmed',
      paymentStatus: 'pending'
    });

    await booking.save();

    // Populate the booking for response
    await booking.populate('gamingStation', 'name type hourlyRate');

    return NextResponse.json(
      { 
        message: 'Booking created successfully',
        booking
      },
      { status: 201 }
    );

  } catch (error) {
    console.error('Error creating booking:', error);
    return NextResponse.json(
      { error: 'Failed to create booking' },
      { status: 500 }
    );
  }
}
