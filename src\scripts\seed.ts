import connectDB from '../lib/mongodb';
import User from '../models/User';
import GamingStation from '../models/GamingStation';
import { hashPassword } from '../lib/auth-utils';

async function seedDatabase() {
  try {
    await connectDB();
    console.log('Connected to MongoDB');

    // Clear existing data
    await User.deleteMany({});
    await GamingStation.deleteMany({});
    console.log('Cleared existing data');

    // Create admin user
    const adminPassword = await hashPassword('admin123');
    const admin = new User({
      name: 'Admin User',
      email: '<EMAIL>',
      password: adminPassword,
      role: 'admin'
    });
    await admin.save();
    console.log('Created admin user');

    // Create sample customer
    const customerPassword = await hashPassword('customer123');
    const customer = new User({
      name: '<PERSON>',
      email: '<EMAIL>',
      password: customerPassword,
      role: 'customer',
      phone: '+1234567890'
    });
    await customer.save();
    console.log('Created sample customer');

    // Create gaming stations
    const stations = [
      {
        name: 'PlayStation 5 Station 1',
        description: 'Latest PlayStation 5 console with 4K gaming, DualSense controller, and premium headset. Perfect for the latest AAA games.',
        type: 'PlayStation 5',
        hourlyRate: 15,
        features: [
          '4K Gaming',
          'DualSense Controller',
          'Premium Headset',
          'Latest Games Library',
          'Comfortable Gaming Chair'
        ],
        isActive: true
      },
      {
        name: 'PlayStation 5 Station 2',
        description: 'High-performance PlayStation 5 setup with racing wheel and premium audio system for the ultimate gaming experience.',
        type: 'PlayStation 5',
        hourlyRate: 18,
        features: [
          '4K Gaming',
          'Racing Wheel Setup',
          'Surround Sound System',
          'VR Compatible',
          'Ergonomic Setup'
        ],
        isActive: true
      },
      {
        name: 'PlayStation 4 Pro Station',
        description: 'PlayStation 4 Pro console with enhanced graphics and extensive game library. Great value for casual gaming.',
        type: 'PlayStation 4',
        hourlyRate: 12,
        features: [
          'Enhanced Graphics',
          'Large Game Library',
          'Wireless Controller',
          'Good Audio System',
          'Comfortable Seating'
        ],
        isActive: true
      },
      {
        name: 'Xbox Series X Station',
        description: 'Microsoft Xbox Series X with Game Pass Ultimate and premium gaming accessories.',
        type: 'Xbox Series X',
        hourlyRate: 16,
        features: [
          'Game Pass Ultimate',
          'Quick Resume',
          'Elite Controller',
          'Premium Headset',
          'High-Speed SSD'
        ],
        isActive: true
      },
      {
        name: 'PC Gaming Station 1',
        description: 'High-end PC gaming setup with RTX 4080, mechanical keyboard, and gaming mouse. Perfect for competitive gaming.',
        type: 'PC Gaming',
        hourlyRate: 20,
        features: [
          'RTX 4080 Graphics',
          'Mechanical Keyboard',
          'Gaming Mouse',
          '144Hz Monitor',
          'Steam Library Access'
        ],
        isActive: true
      },
      {
        name: 'PC Gaming Station 2',
        description: 'Premium PC gaming station with dual monitors, streaming setup, and professional gaming peripherals.',
        type: 'PC Gaming',
        hourlyRate: 25,
        features: [
          'Dual 4K Monitors',
          'Streaming Setup',
          'Professional Peripherals',
          'RGB Lighting',
          'Premium Audio'
        ],
        isActive: true
      }
    ];

    for (const stationData of stations) {
      const station = new GamingStation(stationData);
      await station.save();
    }
    console.log('Created gaming stations');

    console.log('Database seeded successfully!');
    console.log('Admin credentials: <EMAIL> / admin123');
    console.log('Customer credentials: <EMAIL> / customer123');

  } catch (error) {
    console.error('Error seeding database:', error);
  } finally {
    process.exit(0);
  }
}

seedDatabase();
