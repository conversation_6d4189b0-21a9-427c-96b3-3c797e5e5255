'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

interface GamingStation {
  _id: string;
  name: string;
  description: string;
  type: string;
  hourlyRate: number;
  features: string[];
  image?: string;
}

export default function Stations() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [stations, setStations] = useState<GamingStation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchStations();
  }, []);

  const fetchStations = async () => {
    try {
      const response = await fetch('/api/stations');
      const data = await response.json();
      
      if (response.ok) {
        setStations(data.stations);
      } else {
        setError('Failed to fetch gaming stations');
      }
    } catch (error) {
      setError('An error occurred while fetching stations');
    } finally {
      setLoading(false);
    }
  };

  const handleBookNow = (stationId: string) => {
    if (!session) {
      router.push('/auth/signin');
      return;
    }
    router.push(`/stations/${stationId}/book`);
  };

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center">
        <div className="text-white text-xl">Loading...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900">
      {/* Navigation */}
      <nav className="bg-black/20 backdrop-blur-sm border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="text-2xl font-bold text-white">
              Gaming Store
            </Link>
            <div className="flex items-center space-x-4">
              {session ? (
                <>
                  <span className="text-white">Welcome, {session.user.name}</span>
                  <Link
                    href="/bookings"
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                  >
                    My Bookings
                  </Link>
                  <Link
                    href="/api/auth/signout"
                    className="text-white hover:text-gray-300 transition-colors"
                  >
                    Sign Out
                  </Link>
                </>
              ) : (
                <>
                  <Link
                    href="/auth/signin"
                    className="text-white hover:text-gray-300 transition-colors"
                  >
                    Sign In
                  </Link>
                  <Link
                    href="/auth/signup"
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                  >
                    Sign Up
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-white mb-4">Gaming Stations</h1>
          <p className="text-xl text-gray-300">Choose your perfect gaming setup</p>
        </div>

        {error && (
          <div className="bg-red-500/20 border border-red-500 text-red-100 px-4 py-3 rounded mb-6">
            {error}
          </div>
        )}

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {stations.map((station) => (
            <div
              key={station._id}
              className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300"
            >
              <div className="mb-4">
                <h3 className="text-2xl font-bold text-white mb-2">{station.name}</h3>
                <span className="inline-block bg-blue-600 text-white px-3 py-1 rounded-full text-sm">
                  {station.type}
                </span>
              </div>
              
              <p className="text-gray-300 mb-4">{station.description}</p>
              
              {station.features.length > 0 && (
                <div className="mb-4">
                  <h4 className="text-white font-semibold mb-2">Features:</h4>
                  <ul className="text-gray-300 text-sm space-y-1">
                    {station.features.map((feature, index) => (
                      <li key={index} className="flex items-center">
                        <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              
              <div className="flex justify-between items-center">
                <div className="text-white">
                  <span className="text-2xl font-bold">${station.hourlyRate}</span>
                  <span className="text-gray-300">/hour</span>
                </div>
                <button
                  onClick={() => handleBookNow(station._id)}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-semibold transition-colors"
                >
                  Book Now
                </button>
              </div>
            </div>
          ))}
        </div>

        {stations.length === 0 && !loading && !error && (
          <div className="text-center text-gray-300 py-12">
            <p className="text-xl">No gaming stations available at the moment.</p>
          </div>
        )}
      </div>
    </div>
  );
}
