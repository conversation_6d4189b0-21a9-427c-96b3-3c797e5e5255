import mongoose, { Document, Schema } from 'mongoose';

export interface IGamingStation extends Document {
  name: string;
  description: string;
  type: 'PlayStation 4' | 'PlayStation 5' | 'Xbox Series X' | 'PC Gaming';
  hourlyRate: number;
  isActive: boolean;
  features: string[];
  image?: string;
  createdAt: Date;
  updatedAt: Date;
}

const GamingStationSchema = new Schema<IGamingStation>({
  name: {
    type: String,
    required: [true, 'Station name is required'],
    trim: true,
    maxlength: [100, 'Station name cannot be more than 100 characters']
  },
  description: {
    type: String,
    required: [true, 'Description is required'],
    trim: true,
    maxlength: [500, 'Description cannot be more than 500 characters']
  },
  type: {
    type: String,
    required: [true, 'Station type is required'],
    enum: ['PlayStation 4', 'PlayStation 5', 'Xbox Series X', 'PC Gaming']
  },
  hourlyRate: {
    type: Number,
    required: [true, 'Hourly rate is required'],
    min: [0, 'Hourly rate cannot be negative']
  },
  isActive: {
    type: Boolean,
    default: true
  },
  features: [{
    type: String,
    trim: true
  }],
  image: {
    type: String,
    trim: true
  }
}, {
  timestamps: true
});

// Indexes for faster queries
GamingStationSchema.index({ type: 1 });
GamingStationSchema.index({ isActive: 1 });
GamingStationSchema.index({ hourlyRate: 1 });

export default mongoose.models.GamingStation || mongoose.model<IGamingStation>('GamingStation', GamingStationSchema);
