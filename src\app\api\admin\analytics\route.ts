import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Booking from '@/models/Booking';
import GamingStation from '@/models/GamingStation';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    // Get current date ranges
    const now = new Date();
    const startOfToday = new Date(now);
    startOfToday.setHours(0, 0, 0, 0);
    
    const endOfToday = new Date(now);
    endOfToday.setHours(23, 59, 59, 999);
    
    const startOfWeek = new Date(now);
    startOfWeek.setDate(now.getDate() - 7);
    
    const startOfMonth = new Date(now);
    startOfMonth.setDate(now.getDate() - 30);

    // Calculate total revenue and bookings
    const totalStats = await Booking.aggregate([
      {
        $match: {
          status: { $in: ['confirmed', 'completed'] }
        }
      },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$totalAmount' },
          totalBookings: { $sum: 1 }
        }
      }
    ]);

    // Calculate today's revenue and bookings
    const todayStats = await Booking.aggregate([
      {
        $match: {
          status: { $in: ['confirmed', 'completed'] },
          createdAt: { $gte: startOfToday, $lte: endOfToday }
        }
      },
      {
        $group: {
          _id: null,
          todayRevenue: { $sum: '$totalAmount' },
          todayBookings: { $sum: 1 }
        }
      }
    ]);

    // Calculate weekly revenue
    const weeklyStats = await Booking.aggregate([
      {
        $match: {
          status: { $in: ['confirmed', 'completed'] },
          createdAt: { $gte: startOfWeek }
        }
      },
      {
        $group: {
          _id: null,
          weeklyRevenue: { $sum: '$totalAmount' }
        }
      }
    ]);

    // Calculate monthly revenue
    const monthlyStats = await Booking.aggregate([
      {
        $match: {
          status: { $in: ['confirmed', 'completed'] },
          createdAt: { $gte: startOfMonth }
        }
      },
      {
        $group: {
          _id: null,
          monthlyRevenue: { $sum: '$totalAmount' }
        }
      }
    ]);

    // Calculate occupancy rate (simplified - based on today's bookings)
    const totalStations = await GamingStation.countDocuments({ isActive: true });
    const storeHours = 14; // 9 AM to 11 PM = 14 hours
    const totalAvailableHours = totalStations * storeHours;
    
    const todayBookedHours = await Booking.aggregate([
      {
        $match: {
          status: { $in: ['confirmed', 'completed', 'in-progress'] },
          startTime: { $gte: startOfToday, $lte: endOfToday }
        }
      },
      {
        $group: {
          _id: null,
          totalHours: { $sum: '$duration' }
        }
      }
    ]);

    const occupancyRate = totalAvailableHours > 0 
      ? Math.round(((todayBookedHours[0]?.totalHours || 0) / totalAvailableHours) * 100)
      : 0;

    const analytics = {
      totalRevenue: totalStats[0]?.totalRevenue || 0,
      totalBookings: totalStats[0]?.totalBookings || 0,
      todayRevenue: todayStats[0]?.todayRevenue || 0,
      todayBookings: todayStats[0]?.todayBookings || 0,
      weeklyRevenue: weeklyStats[0]?.weeklyRevenue || 0,
      monthlyRevenue: monthlyStats[0]?.monthlyRevenue || 0,
      occupancyRate
    };

    return NextResponse.json(analytics);

  } catch (error) {
    console.error('Error fetching analytics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch analytics' },
      { status: 500 }
    );
  }
}
