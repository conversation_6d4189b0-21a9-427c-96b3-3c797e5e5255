'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useParams } from 'next/navigation';
import Link from 'next/link';

interface TimeSlot {
  startTime: string;
  endTime: string;
  duration: number;
  price: number;
}

interface Station {
  id: string;
  name: string;
  type: string;
  hourlyRate: number;
}

export default function BookStation() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const params = useParams();
  const stationId = params.id as string;

  const [station, setStation] = useState<Station | null>(null);
  const [selectedDate, setSelectedDate] = useState('');
  const [availableSlots, setAvailableSlots] = useState<TimeSlot[]>([]);
  const [selectedSlot, setSelectedSlot] = useState<TimeSlot | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
      return;
    }

    // Set default date to today
    const today = new Date();
    setSelectedDate(today.toISOString().split('T')[0]);
  }, [status, router]);

  useEffect(() => {
    if (selectedDate) {
      fetchAvailability();
    }
  }, [selectedDate, stationId]);

  const fetchAvailability = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/stations/${stationId}/availability?date=${selectedDate}`);
      const data = await response.json();
      
      if (response.ok) {
        setStation(data.station);
        setAvailableSlots(data.availableSlots);
      } else {
        setError(data.error || 'Failed to fetch availability');
      }
    } catch (error) {
      setError('An error occurred while fetching availability');
    } finally {
      setLoading(false);
    }
  };

  const handleBooking = async () => {
    if (!selectedSlot) return;

    setLoading(true);
    try {
      const response = await fetch('/api/bookings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          gamingStationId: stationId,
          startTime: selectedSlot.startTime,
          endTime: selectedSlot.endTime,
          duration: selectedSlot.duration,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        router.push('/bookings?success=true');
      } else {
        setError(data.error || 'Failed to create booking');
      }
    } catch (error) {
      setError('An error occurred while creating the booking');
    } finally {
      setLoading(false);
    }
  };

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center">
        <div className="text-white text-xl">Loading...</div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900">
      {/* Navigation */}
      <nav className="bg-black/20 backdrop-blur-sm border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="text-2xl font-bold text-white">
              Gaming Store
            </Link>
            <div className="flex items-center space-x-4">
              <Link
                href="/stations"
                className="text-white hover:text-gray-300 transition-colors"
              >
                Back to Stations
              </Link>
              <Link
                href="/bookings"
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                My Bookings
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">Book Gaming Session</h1>
          {station && (
            <div className="text-xl text-gray-300">
              <p>{station.name} - {station.type}</p>
              <p>${station.hourlyRate}/hour</p>
            </div>
          )}
        </div>

        {error && (
          <div className="bg-red-500/20 border border-red-500 text-red-100 px-4 py-3 rounded mb-6">
            {error}
          </div>
        )}

        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-8 border border-white/20">
          {/* Date Selection */}
          <div className="mb-8">
            <label className="block text-white text-lg font-semibold mb-4">
              Select Date
            </label>
            <input
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              min={new Date().toISOString().split('T')[0]}
              className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Time Slots */}
          <div className="mb-8">
            <h3 className="text-white text-lg font-semibold mb-4">Available Time Slots</h3>
            
            {loading ? (
              <div className="text-center text-white py-8">Loading available slots...</div>
            ) : availableSlots.length > 0 ? (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {availableSlots.map((slot, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedSlot(slot)}
                    className={`p-4 rounded-lg border-2 transition-all ${
                      selectedSlot === slot
                        ? 'border-blue-400 bg-blue-500/20 text-white'
                        : 'border-white/20 bg-white/10 text-gray-300 hover:border-blue-400 hover:bg-blue-500/10'
                    }`}
                  >
                    <div className="text-sm font-semibold">
                      {new Date(slot.startTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} - 
                      {new Date(slot.endTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </div>
                    <div className="text-xs mt-1">
                      {slot.duration} hour{slot.duration > 1 ? 's' : ''} - ${slot.price}
                    </div>
                  </button>
                ))}
              </div>
            ) : (
              <div className="text-center text-gray-300 py-8">
                No available slots for this date. Please select a different date.
              </div>
            )}
          </div>

          {/* Booking Summary */}
          {selectedSlot && (
            <div className="bg-white/10 rounded-lg p-6 mb-6">
              <h3 className="text-white text-lg font-semibold mb-4">Booking Summary</h3>
              <div className="space-y-2 text-gray-300">
                <p><span className="font-semibold">Station:</span> {station?.name}</p>
                <p><span className="font-semibold">Date:</span> {new Date(selectedDate).toLocaleDateString()}</p>
                <p><span className="font-semibold">Time:</span> {new Date(selectedSlot.startTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} - {new Date(selectedSlot.endTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</p>
                <p><span className="font-semibold">Duration:</span> {selectedSlot.duration} hour{selectedSlot.duration > 1 ? 's' : ''}</p>
                <p><span className="font-semibold text-xl text-white">Total: ${selectedSlot.price}</span></p>
              </div>
            </div>
          )}

          {/* Book Button */}
          <div className="text-center">
            <button
              onClick={handleBooking}
              disabled={!selectedSlot || loading}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-500 disabled:cursor-not-allowed text-white px-8 py-3 rounded-lg text-lg font-semibold transition-colors"
            >
              {loading ? 'Booking...' : 'Confirm Booking'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
