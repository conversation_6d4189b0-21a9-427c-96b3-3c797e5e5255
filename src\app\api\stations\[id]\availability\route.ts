import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Booking from '@/models/Booking';
import GamingStation from '@/models/GamingStation';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date');
    
    if (!date) {
      return NextResponse.json(
        { error: 'Date parameter is required' },
        { status: 400 }
      );
    }

    await connectDB();

    // Check if station exists
    const station = await GamingStation.findById(params.id);
    if (!station || !station.isActive) {
      return NextResponse.json(
        { error: 'Gaming station not found or inactive' },
        { status: 404 }
      );
    }

    // Get start and end of the requested date
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);

    // Find all bookings for this station on this date
    const bookings = await Booking.find({
      gamingStation: params.id,
      status: { $in: ['confirmed', 'in-progress'] },
      startTime: { $gte: startOfDay, $lte: endOfDay }
    }).sort({ startTime: 1 });

    // Generate available time slots (assuming store hours 9 AM to 11 PM)
    const storeOpenHour = 9;
    const storeCloseHour = 23;
    const slotDuration = 1; // 1 hour slots
    
    const availableSlots = [];
    
    for (let hour = storeOpenHour; hour < storeCloseHour; hour++) {
      const slotStart = new Date(startOfDay);
      slotStart.setHours(hour, 0, 0, 0);
      
      const slotEnd = new Date(startOfDay);
      slotEnd.setHours(hour + slotDuration, 0, 0, 0);

      // Check if this slot conflicts with any booking
      const isBooked = bookings.some(booking => {
        return (
          (booking.startTime < slotEnd && booking.endTime > slotStart)
        );
      });

      if (!isBooked) {
        availableSlots.push({
          startTime: slotStart.toISOString(),
          endTime: slotEnd.toISOString(),
          duration: slotDuration,
          price: station.hourlyRate * slotDuration
        });
      }
    }

    return NextResponse.json({
      station: {
        id: station._id,
        name: station.name,
        type: station.type,
        hourlyRate: station.hourlyRate
      },
      date,
      availableSlots,
      bookedSlots: bookings.map(booking => ({
        startTime: booking.startTime,
        endTime: booking.endTime
      }))
    });

  } catch (error) {
    console.error('Error fetching availability:', error);
    return NextResponse.json(
      { error: 'Failed to fetch availability' },
      { status: 500 }
    );
  }
}
