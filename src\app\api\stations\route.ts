import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import GamingStation from '@/models/GamingStation';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET() {
  try {
    await connectDB();
    
    const stations = await GamingStation.find({ isActive: true }).sort({ name: 1 });
    
    return NextResponse.json({ stations });
  } catch (error) {
    console.error('Error fetching stations:', error);
    return NextResponse.json(
      { error: 'Failed to fetch gaming stations' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { name, description, type, hourlyRate, features, image } = await request.json();

    if (!name || !description || !type || !hourlyRate) {
      return NextResponse.json(
        { error: 'Name, description, type, and hourly rate are required' },
        { status: 400 }
      );
    }

    await connectDB();

    const station = new GamingStation({
      name,
      description,
      type,
      hourlyRate,
      features: features || [],
      image,
      isActive: true
    });

    await station.save();

    return NextResponse.json(
      { 
        message: 'Gaming station created successfully',
        station
      },
      { status: 201 }
    );

  } catch (error) {
    console.error('Error creating station:', error);
    return NextResponse.json(
      { error: 'Failed to create gaming station' },
      { status: 500 }
    );
  }
}
